package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nacos.entity.dto.VideoTranslateRequestDTO;
import com.nacos.entity.po.VideoTranslateTaskPO;
import com.nacos.entity.vo.VideoTranslateStatusVO;
import com.nacos.entity.vo.LanguageVO;
import com.nacos.entity.vo.VoiceVO;
import com.nacos.mapper.VideoTranslateTaskMapper;
import com.nacos.model.SoundView.SoundViewApi;
import com.nacos.model.SoundView.enums.VideoTranslateStatusEnum;
import com.nacos.model.SoundView.model.response.LanguageListResponseBO;
import com.nacos.model.SoundView.model.response.VoiceListResponseBO;
import com.nacos.result.Result;
import com.nacos.service.VideoTranslateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.nacos.utils.VideoFileValidator;
import com.nacos.utils.DigitalFileUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 视频翻译服务实现类
 * 
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VideoTranslateServiceImpl implements VideoTranslateService {

    private final VideoTranslateTaskMapper videoTranslateTaskMapper;

    /**
     * 提交视频翻译任务
     * 只创建任务记录，由定时任务触发执行
     *
     * @param request 翻译请求参数
     * @param file    视频文件
     * @return 任务提交结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Map<String, Object>> submitTranslateTask(VideoTranslateRequestDTO request, MultipartFile file) {
        String methodName = "submitTranslateTask";
        try {
            log.info("[{}] 开始提交视频翻译任务: userId={}, fileName={}, fileSize={}, {}",
                    methodName, request.getUserId(), file.getOriginalFilename(), file.getSize(),
                    request.getLanguagePairDescription());

            // 1. 文件必填验证（控制器已验证，这里再次确认）
            if (file == null || file.isEmpty()) {
                log.warn("[{}] 文件验证失败: 视频文件不能为空, userId={}", methodName, request.getUserId());
                return Result.ERROR("视频文件不能为空");
            }

            // 2. 文件验证
            VideoFileValidator.ValidationResult validationResult = VideoFileValidator.validateVideoFile(file);
            if (!validationResult.isValid()) {
                log.warn("[{}] 文件验证失败: userId={}, error={}", methodName, request.getUserId(),
                        validationResult.getErrorMessage());
                return Result.ERROR(validationResult.getErrorMessage());
            }

            // 3. 文件上传到OSS
            try {
                log.info("[{}] 开始上传文件到OSS: userId={}, fileName={}", methodName, request.getUserId(),
                        file.getOriginalFilename());

                // 使用type=13上传到系统视频临时目录
                String uploadedUrl = DigitalFileUtil.uploadDigitalResource(
                        file, // 直接传递MultipartFile
                        UUID.randomUUID().toString(),
                        request.getUserId(),
                        null, // groupId为null
                        13, // 上传系统视频临时目录
                        true // 系统文件
                );

                if (uploadedUrl == null || uploadedUrl.trim().isEmpty()) {
                    log.error("[{}] 文件上传失败: userId={}, fileName={}", methodName, request.getUserId(),
                            file.getOriginalFilename());
                    return Result.ERROR("文件上传失败，请重试");
                }

                // 设置上传后的URL到请求参数
                request.setVideoUrl(uploadedUrl);
                log.info("[{}] 文件上传成功: userId={}, uploadedUrl={}", methodName, request.getUserId(), uploadedUrl);

            } catch (Exception e) {
                log.error("[{}] 文件上传异常: userId={}, fileName={}, error={}",
                        methodName, request.getUserId(), file.getOriginalFilename(), e.getMessage(), e);
                return Result.ERROR("文件上传失败: " + e.getMessage());
            }

            // 4. 参数验证（包括videoUrl验证）
            if (!request.isValidWithVideoUrl()) {
                return Result.ERROR("请求参数无效");
            }

            if (request.isSameLanguage()) {
                return Result.ERROR("源语言和目标语言不能相同");
            }

            // 设置默认值
            request.setDefaults();

            // 2. 生成任务ID
            String taskId = UUID.randomUUID().toString().replace("-", "");

            // 3. 创建本地任务记录
            // 构建请求参数JSON
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("sourceLanguage", request.getSourceLanguage());
            requestParams.put("targetLanguage", request.getTargetLanguage());
            requestParams.put("voiceId", request.getVoiceId());
            requestParams.put("useOriginalVoice", request.getUseOriginalVoice());
            if (request.getExtParams() != null) {
                requestParams.put("extParams", request.getExtParams());
            }

            VideoTranslateTaskPO taskPO = VideoTranslateTaskPO.builder()
                    .taskId(taskId)
                    .userId(request.getUserId())
                    .taskName(request.getDefaultTaskName())
                    .sourceVideoUrl(request.getVideoUrl())
                    .status(0) // 状态为0-排队中，等待定时任务处理
                    .requestParamsJson(convertToJson(requestParams)) // 保存请求参数
                    .createdTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .isDeleted(0)
                    .build();

            // 4. 保存到数据库
            int insertResult = videoTranslateTaskMapper.insert(taskPO);
            if (insertResult <= 0) {
                log.error("[{}] 保存任务记录失败: taskId={}", methodName, taskId);
                return Result.ERROR("保存任务记录失败");
            }

            log.info("[{}] 任务记录创建成功，等待定时任务处理: taskId={}", methodName, taskId);

            // 5. 返回结果（不再直接调用第三方API）
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("status", "submitted");
            result.put("submitTime", LocalDateTime.now().toString());
            result.put("message", "任务已提交，等待处理");

            return Result.SUCCESS("任务提交成功", result);

        } catch (Exception e) {
            log.error("[{}] 提交视频翻译任务异常: userId={}, error={}", methodName, request.getUserId(), e.getMessage(), e);
            return Result.ERROR("提交任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询任务状态
     */
    @Override
    public Result<VideoTranslateStatusVO> getTaskStatus(String taskId) {
        String methodName = "getTaskStatus";
        try {
            log.info("[{}] 查询任务状态: taskId={}", methodName, taskId);

            // 1. 查询本地任务记录
            VideoTranslateTaskPO taskPO = videoTranslateTaskMapper.getTaskByTaskId(taskId);
            if (taskPO == null) {
                log.error("[{}] 任务不存在: taskId={}", methodName, taskId);
                return Result.ERROR("任务不存在");
            }

            // 2. 如果任务还在处理中，尝试同步状态
            if (taskPO.isProcessing()) {
                syncSingleTaskStatus(taskPO);
                // 重新查询更新后的状态
                taskPO = videoTranslateTaskMapper.getTaskByTaskId(taskId);
            }

            // 3. 构建返回结果
            VideoTranslateStatusVO statusVO = buildStatusVO(taskPO);

            log.info("[{}] 查询任务状态成功: taskId={}, status={}", methodName, taskId, taskPO.getStatus());
            return Result.SUCCESS(statusVO);

        } catch (Exception e) {
            log.error("[{}] 查询任务状态异常: taskId={}, error={}", methodName, taskId, e.getMessage(), e);
            return Result.ERROR("查询任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 处理排队中的任务
     */
    @Override
    public void processQueueingTasks() {
        String methodName = "processQueueingTasks";
        try {
            log.info("[{}] 开始处理排队中的任务", methodName);

            // 查询状态为submitted的任务
            List<VideoTranslateTaskPO> queueingTasks = videoTranslateTaskMapper.getTasksByStatus("submitted", 10);

            if (queueingTasks.isEmpty()) {
                log.info("[{}] 没有排队中的任务", methodName);
                return;
            }

            log.info("[{}] 找到{}个排队中的任务", methodName, queueingTasks.size());

            for (VideoTranslateTaskPO task : queueingTasks) {
                try {
                    // 同步任务状态
                    syncSingleTaskStatus(task);
                } catch (Exception e) {
                    log.error("[{}] 处理任务异常: taskId={}, error={}", methodName, task.getTaskId(), e.getMessage(), e);
                }
            }

            log.info("[{}] 排队中任务处理完成", methodName);

        } catch (Exception e) {
            log.error("[{}] 处理排队中任务异常: error={}", methodName, e.getMessage(), e);
        }
    }

    /**
     * 处理超时任务
     */
    @Override
    public void processTimeoutTasks() {
        String methodName = "processTimeoutTasks";
        try {
            log.info("[{}] 开始处理超时任务", methodName);

            // 查询超时任务（30分钟超时）
            List<VideoTranslateTaskPO> timeoutTasks = videoTranslateTaskMapper.getTimeoutTasks(30);

            if (timeoutTasks.isEmpty()) {
                log.info("[{}] 没有超时任务", methodName);
                return;
            }

            log.info("[{}] 找到{}个超时任务", methodName, timeoutTasks.size());

            for (VideoTranslateTaskPO task : timeoutTasks) {
                try {
                    // 标记任务超时
                    updateTaskStatus(task.getTaskId(), 4, "任务处理时间超过30分钟", null);
                    log.info("[{}] 任务已标记为超时: taskId={}", methodName, task.getTaskId());
                } catch (Exception e) {
                    log.error("[{}] 处理超时任务异常: taskId={}, error={}", methodName, task.getTaskId(), e.getMessage(), e);
                }
            }

            log.info("[{}] 超时任务处理完成", methodName);

        } catch (Exception e) {
            log.error("[{}] 处理超时任务异常: error={}", methodName, e.getMessage(), e);
        }
    }

    /**
     * 同步任务状态
     */
    @Override
    public void syncTaskStatus() {
        String methodName = "syncTaskStatus";
        try {
            log.info("[{}] 开始同步任务状态", methodName);

            // 查询处理中的任务
            List<VideoTranslateTaskPO> processingTasks = videoTranslateTaskMapper.getProcessingTasks(20);

            if (processingTasks.isEmpty()) {
                log.info("[{}] 没有需要同步的任务", methodName);
                return;
            }

            log.info("[{}] 找到{}个需要同步的任务", methodName, processingTasks.size());

            for (VideoTranslateTaskPO task : processingTasks) {
                try {
                    syncSingleTaskStatus(task);
                } catch (Exception e) {
                    log.error("[{}] 同步任务状态异常: taskId={}, error={}", methodName, task.getTaskId(), e.getMessage(), e);
                }
            }

            log.info("[{}] 任务状态同步完成", methodName);

        } catch (Exception e) {
            log.error("[{}] 同步任务状态异常: error={}", methodName, e.getMessage(), e);
        }
    }

    // ==================== 其他接口方法的简化实现 ====================

    @Override
    public Result<VideoTranslateStatusVO> getTaskByLingyangTaskId(String lingyangTaskId) {
        try {
            VideoTranslateTaskPO taskPO = videoTranslateTaskMapper.getTaskByLingyangTaskId(lingyangTaskId);
            if (taskPO == null) {
                return Result.ERROR("任务不存在");
            }
            return Result.SUCCESS(buildStatusVO(taskPO));
        } catch (Exception e) {
            log.error("根据羚羊任务ID查询失败: lingyangTaskId={}, error={}", lingyangTaskId, e.getMessage(), e);
            return Result.ERROR("查询失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> updateTaskStatus(String taskId, String status, Integer progress,
            String currentStep, String errorMessage) {
        // 转换旧的字符串状态为新的数字状态
        Integer newStatus = convertStringStatusToInteger(status);
        return updateTaskStatus(taskId, newStatus, errorMessage, null);
    }

    /**
     * 新的更新任务状态方法（适配新字段结构）
     */
    public Result<Boolean> updateTaskStatus(String taskId, Integer status, String errorMsg, String resultJson) {
        try {
            LambdaUpdateWrapper<VideoTranslateTaskPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(VideoTranslateTaskPO::getTaskId, taskId)
                    .set(VideoTranslateTaskPO::getStatus, status)
                    .set(VideoTranslateTaskPO::getUpdateTime, LocalDateTime.now());

            if (StringUtils.isNotBlank(errorMsg)) {
                updateWrapper.set(VideoTranslateTaskPO::getErrorMsg, errorMsg);
            }

            if (StringUtils.isNotBlank(resultJson)) {
                updateWrapper.set(VideoTranslateTaskPO::getResultJson, resultJson);
            }

            int result = videoTranslateTaskMapper.update(null, updateWrapper);
            return Result.SUCCESS(result > 0);
        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}, status={}, error={}", taskId, status, e.getMessage(), e);
            return Result.ERROR("更新失败: " + e.getMessage());
        }
    }

    /**
     * 转换旧的字符串状态为新的数字状态
     */
    private Integer convertStringStatusToInteger(String status) {
        if (status == null)
            return 0;
        switch (status) {
            case "submitted":
                return 0; // 排队中
            case "processing":
                return 1; // 进行中
            case "completed":
                return 2; // 翻译成功
            case "failed":
                return 3; // 失败
            case "timeout":
                return 4; // 超时
            case "cancelled":
                return 5; // 已取消
            default:
                return 0;
        }
    }

    @Override
    public Result<Boolean> completeTask(String taskId, String resultVideoUrl, String subtitleUrl,
            String originalText, String translatedText) {
        try {
            // 构建结果JSON
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("resultVideoUrl", resultVideoUrl);
            resultData.put("subtitleUrl", subtitleUrl);
            resultData.put("originalText", originalText);
            resultData.put("translatedText", translatedText);

            LambdaUpdateWrapper<VideoTranslateTaskPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(VideoTranslateTaskPO::getTaskId, taskId)
                    .set(VideoTranslateTaskPO::getStatus, 2) // 翻译成功
                    .set(VideoTranslateTaskPO::getTranslatedVideoUrl, resultVideoUrl)
                    .set(VideoTranslateTaskPO::getResultJson, convertToJson(resultData))
                    .set(VideoTranslateTaskPO::getUpdateTime, LocalDateTime.now());

            int result = videoTranslateTaskMapper.update(null, updateWrapper);
            return Result.SUCCESS(result > 0);
        } catch (Exception e) {
            log.error("完成任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return Result.ERROR("完成任务失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> failTask(String taskId, String errorMessage) {
        return updateTaskStatus(taskId, 3, errorMessage, null);
    }

    @Override
    public Result<Map<String, Object>> getUserTaskStats(String userId) {
        try {
            Map<String, Object> stats = new HashMap<>();
            stats.put("total", videoTranslateTaskMapper.countTasksByUser(userId, null));
            stats.put("completed", videoTranslateTaskMapper.countTasksByUser(userId, "completed"));
            stats.put("processing", videoTranslateTaskMapper.countTasksByUser(userId, "processing"));
            stats.put("failed", videoTranslateTaskMapper.countTasksByUser(userId, "failed"));
            return Result.SUCCESS(stats);
        } catch (Exception e) {
            log.error("获取用户任务统计失败: userId={}, error={}", userId, e.getMessage(), e);
            return Result.ERROR("获取统计失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> retryTask(String taskId, String userId) {
        // 重试逻辑的简化实现
        try {
            VideoTranslateTaskPO task = videoTranslateTaskMapper.getTaskByTaskId(taskId);
            if (task == null || !userId.equals(task.getUserId())) {
                return Result.ERROR("任务不存在或无权限");
            }

            if (!task.isFailed()) {
                return Result.ERROR("只能重试失败或超时的任务");
            }

            updateTaskStatus(taskId, 0, "任务重新提交", null);
            return Result.SUCCESS("任务已重新提交");
        } catch (Exception e) {
            log.error("重试任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return Result.ERROR("重试失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 同步单个任务状态
     */
    private void syncSingleTaskStatus(VideoTranslateTaskPO task) {
        String methodName = "syncSingleTaskStatus";
        try {
            // 简化的状态同步逻辑（实际项目中需要调用服务商API）
            log.info("[{}] 同步任务状态: taskId={}, provider={}, status={}",
                    methodName, task.getTaskId(), task.getProvider(), task.getStatus());

            // 模拟状态同步（实际应该调用服务商API）
            // TODO: 实现真实的服务商API调用
            if (task.getStatus() == 0) { // 排队中
                updateTaskStatus(task.getTaskId(), 1, null, null); // 更新为进行中
            }

        } catch (Exception e) {
            log.error("[{}] 同步任务状态异常: taskId={}, error={}", methodName, task.getTaskId(), e.getMessage(), e);
        }
    }

    /**
     * 构建状态VO对象
     */
    private VideoTranslateStatusVO buildStatusVO(VideoTranslateTaskPO taskPO) {
        VideoTranslateStatusVO vo = new VideoTranslateStatusVO();
        vo.setTaskId(taskPO.getTaskId());
        vo.setUserId(taskPO.getUserId());
        vo.setTaskName(taskPO.getTaskName());
        vo.setSourceVideoUrl(taskPO.getSourceVideoUrl());
        vo.setStatus(convertIntegerStatusToString(taskPO.getStatus()));
        vo.setErrorMessage(taskPO.getErrorMsg());
        vo.setCreatedTime(taskPO.getCreatedTime());
        vo.setUpdateTime(taskPO.getUpdateTime());

        // 设置结果URL（兼容旧字段名）
        vo.setResultVideoUrl(taskPO.getTranslatedVideoUrl());
        vo.setCoverUrl(taskPO.getCoverUrl());

        // 从JSON字段中提取详细信息
        extractFromRequestParams(vo, taskPO.getRequestParamsJson());
        extractFromResultJson(vo, taskPO.getResultJson());

        // 设置状态描述
        vo.setStatusDesc(taskPO.getStatusDescription());
        return vo;
    }

    /**
     * 获取状态描述
     */
    private String getStatusDescription(String status) {
        if (status == null) {
            return "未知状态";
        }
        switch (status) {
            case "submitted":
                return "已提交";
            case "waiting":
                return "等待处理";
            case "preparing":
                return "准备中";
            case "uploading":
                return "上传中";
            case "processing":
                return "处理中";
            case "completed":
                return "已完成";
            case "failed":
                return "处理失败";
            case "timeout":
                return "处理超时";
            case "cancelled":
                return "已取消";
            default:
                return "未知状态";
        }
    }

    // ==================== 任务历史查询功能实现 ====================

    /**
     * 分页查询任务历史
     */
    @Override
    public Result<Map<String, Object>> queryTaskHistory(String userId, Integer status,
            LocalDateTime startTime, LocalDateTime endTime,
            Integer page, Integer size,
            String sortBy, String sortOrder) {
        String methodName = "queryTaskHistory";
        try {
            log.debug("[{}] 查询任务历史: userId={}, status={}, page={}, size={}",
                    methodName, userId, status, page, size);

            // 参数校验和默认值设置
            if (page == null || page < 1)
                page = 1;
            if (size == null || size < 1)
                size = 10;
            if (size > 100)
                size = 100; // 限制最大页面大小
            if (sortBy == null)
                sortBy = "createdTime";
            if (sortOrder == null)
                sortOrder = "DESC";

            // 构建查询条件
            LambdaQueryWrapper<VideoTranslateTaskPO> queryWrapper = new LambdaQueryWrapper<VideoTranslateTaskPO>()
                    .eq(VideoTranslateTaskPO::getIsDeleted, 0);

            // 用户条件
            if (userId != null && !userId.trim().isEmpty()) {
                queryWrapper.eq(VideoTranslateTaskPO::getUserId, userId);
            }

            // 状态条件
            if (status != null) {
                queryWrapper.eq(VideoTranslateTaskPO::getStatus, status);
            }

            // 时间范围条件
            if (startTime != null) {
                queryWrapper.ge(VideoTranslateTaskPO::getCreatedTime, startTime);
            }
            if (endTime != null) {
                queryWrapper.le(VideoTranslateTaskPO::getCreatedTime, endTime);
            }

            // 排序
            if ("ASC".equalsIgnoreCase(sortOrder)) {
                switch (sortBy.toLowerCase()) {
                    case "createdtime":
                        queryWrapper.orderByAsc(VideoTranslateTaskPO::getCreatedTime);
                        break;
                    case "starttime":
                        queryWrapper.orderByAsc(VideoTranslateTaskPO::getCreatedTime); // 使用创建时间代替开始时间
                        break;
                    case "finishtime":
                        queryWrapper.orderByAsc(VideoTranslateTaskPO::getUpdateTime); // 使用更新时间代替完成时间
                        break;
                    case "updatetime":
                        queryWrapper.orderByAsc(VideoTranslateTaskPO::getUpdateTime);
                        break;
                    default:
                        queryWrapper.orderByAsc(VideoTranslateTaskPO::getCreatedTime);
                }
            } else {
                switch (sortBy.toLowerCase()) {
                    case "createdtime":
                        queryWrapper.orderByDesc(VideoTranslateTaskPO::getCreatedTime);
                        break;
                    case "starttime":
                        queryWrapper.orderByDesc(VideoTranslateTaskPO::getCreatedTime); // 使用创建时间代替开始时间
                        break;
                    case "finishtime":
                        queryWrapper.orderByDesc(VideoTranslateTaskPO::getUpdateTime); // 使用更新时间代替完成时间
                        break;
                    case "updatetime":
                        queryWrapper.orderByDesc(VideoTranslateTaskPO::getUpdateTime);
                        break;
                    default:
                        queryWrapper.orderByDesc(VideoTranslateTaskPO::getCreatedTime);
                }
            }

            // 分页查询
            Page<VideoTranslateTaskPO> pageParam = new Page<>(page, size);
            Page<VideoTranslateTaskPO> pageResult = videoTranslateTaskMapper.selectPage(pageParam, queryWrapper);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", pageResult.getRecords());
            result.put("total", pageResult.getTotal());
            result.put("pages", pageResult.getPages());
            result.put("current", pageResult.getCurrent());
            result.put("size", pageResult.getSize());
            result.put("hasNext", pageResult.hasNext());
            result.put("hasPrevious", pageResult.hasPrevious());

            // 添加查询条件信息
            Map<String, Object> queryInfo = new HashMap<>();
            queryInfo.put("userId", userId);
            queryInfo.put("status", status);
            queryInfo.put("startTime", startTime);
            queryInfo.put("endTime", endTime);
            queryInfo.put("sortBy", sortBy);
            queryInfo.put("sortOrder", sortOrder);
            result.put("queryInfo", queryInfo);

            log.info("[{}] 查询任务历史成功: 共{}条记录", methodName, pageResult.getTotal());
            return Result.SUCCESS(result);

        } catch (Exception e) {
            log.error("[{}] 查询任务历史异常: error={}", methodName, e.getMessage(), e);
            return Result.ERROR("查询任务历史异常: " + e.getMessage());
        }
    }

    /**
     * 获取用户任务历史统计
     */
    @Override
    public Result<Map<String, Object>> getUserTaskHistoryStats(String userId, Integer days) {
        String methodName = "getUserTaskHistoryStats";
        try {
            log.debug("[{}] 获取用户任务历史统计: userId={}, days={}", methodName, userId, days);

            if (userId == null || userId.trim().isEmpty()) {
                return Result.ERROR("用户ID不能为空");
            }

            if (days == null || days < 1) {
                days = 30; // 默认30天
            }

            LocalDateTime startTime = LocalDateTime.now().minusDays(days);

            // 构建查询条件
            LambdaQueryWrapper<VideoTranslateTaskPO> baseQuery = new LambdaQueryWrapper<VideoTranslateTaskPO>()
                    .eq(VideoTranslateTaskPO::getUserId, userId)
                    .eq(VideoTranslateTaskPO::getIsDeleted, 0)
                    .ge(VideoTranslateTaskPO::getCreatedTime, startTime);

            Map<String, Object> stats = new HashMap<>();

            // 总任务数
            Long totalTasks = videoTranslateTaskMapper.selectCount(baseQuery);
            stats.put("totalTasks", totalTasks);

            // 各状态任务数
            for (VideoTranslateStatusEnum status : VideoTranslateStatusEnum.values()) {
                Long count = videoTranslateTaskMapper.selectCount(
                        baseQuery.clone().eq(VideoTranslateTaskPO::getStatus, status.getCode()));
                stats.put(status.getCode() + "Count", count);
            }

            // 成功率
            Long successCount = videoTranslateTaskMapper.selectCount(
                    baseQuery.clone().eq(VideoTranslateTaskPO::getStatus,
                            VideoTranslateStatusEnum.COMPLETED.getCode()));
            double successRate = totalTasks > 0 ? (successCount.doubleValue() / totalTasks * 100) : 0;
            stats.put("successRate", Math.round(successRate * 100.0) / 100.0);

            // 平均处理时间（仅计算已完成的任务）
            List<VideoTranslateTaskPO> completedTasks = videoTranslateTaskMapper.selectList(
                    baseQuery.clone()
                            .eq(VideoTranslateTaskPO::getStatus, 2) // 2 = 翻译成功
                            .isNotNull(VideoTranslateTaskPO::getDurationMs));

            if (!completedTasks.isEmpty()) {
                long totalDuration = 0;
                for (VideoTranslateTaskPO task : completedTasks) {
                    if (task.getDurationMs() != null) {
                        totalDuration += task.getDurationMs() / 1000; // 转换为秒
                    }
                }
                long avgDuration = totalDuration / completedTasks.size();
                stats.put("avgProcessingSeconds", avgDuration);
                stats.put("avgProcessingDesc", formatDuration(avgDuration));
            } else {
                stats.put("avgProcessingSeconds", 0);
                stats.put("avgProcessingDesc", "无数据");
            }

            // 统计时间范围
            stats.put("statisticsDays", days);
            stats.put("startTime", startTime);
            stats.put("endTime", LocalDateTime.now());

            return Result.SUCCESS(stats);

        } catch (Exception e) {
            log.error("[{}] 获取用户任务历史统计异常: userId={}, error={}", methodName, userId, e.getMessage(), e);
            return Result.ERROR("获取用户任务历史统计异常: " + e.getMessage());
        }
    }

    /**
     * 导出任务历史数据
     */
    @Override
    public Result<Map<String, Object>> exportTaskHistory(String userId, Integer status,
            LocalDateTime startTime, LocalDateTime endTime,
            String format) {
        String methodName = "exportTaskHistory";
        try {
            log.debug("[{}] 导出任务历史: userId={}, status={}, format={}", methodName, userId, status, format);

            // 参数校验
            if (format == null || (!format.equalsIgnoreCase("CSV") && !format.equalsIgnoreCase("EXCEL"))) {
                format = "CSV"; // 默认CSV格式
            }

            // 构建查询条件
            LambdaQueryWrapper<VideoTranslateTaskPO> queryWrapper = new LambdaQueryWrapper<VideoTranslateTaskPO>()
                    .eq(VideoTranslateTaskPO::getIsDeleted, 0);

            if (userId != null && !userId.trim().isEmpty()) {
                queryWrapper.eq(VideoTranslateTaskPO::getUserId, userId);
            }

            if (status != null) {
                queryWrapper.eq(VideoTranslateTaskPO::getStatus, status);
            }

            if (startTime != null) {
                queryWrapper.ge(VideoTranslateTaskPO::getCreatedTime, startTime);
            }

            if (endTime != null) {
                queryWrapper.le(VideoTranslateTaskPO::getCreatedTime, endTime);
            }

            // 限制导出数量（防止数据过大）
            queryWrapper.orderByDesc(VideoTranslateTaskPO::getCreatedTime)
                    .last("LIMIT 10000");

            List<VideoTranslateTaskPO> tasks = videoTranslateTaskMapper.selectList(queryWrapper);

            // 生成导出文件信息
            Map<String, Object> exportInfo = new HashMap<>();
            exportInfo.put("format", format.toUpperCase());
            exportInfo.put("recordCount", tasks.size());
            exportInfo.put("exportTime", LocalDateTime.now());
            exportInfo.put("fileName", generateExportFileName(userId, format));

            // 这里可以实际生成文件并返回下载链接
            // 示例：exportInfo.put("downloadUrl", fileService.generateExportFile(tasks,
            // format));
            exportInfo.put("downloadUrl", "/api/video-translate/download/" + exportInfo.get("fileName"));

            log.info("[{}] 导出任务历史成功: 共{}条记录", methodName, tasks.size());
            return Result.SUCCESS(exportInfo);

        } catch (Exception e) {
            log.error("[{}] 导出任务历史异常: error={}", methodName, e.getMessage(), e);
            return Result.ERROR("导出任务历史异常: " + e.getMessage());
        }
    }

    /**
     * 获取任务执行趋势数据
     */
    @Override
    public Result<Map<String, Object>> getTaskTrendData(String userId, Integer days, String granularity) {
        String methodName = "getTaskTrendData";
        try {
            log.debug("[{}] 获取任务执行趋势: userId={}, days={}, granularity={}",
                    methodName, userId, days, granularity);

            if (days == null || days < 1) {
                days = 7; // 默认7天
            }

            if (granularity == null) {
                granularity = "DAY"; // 默认按天统计
            }

            LocalDateTime startTime = LocalDateTime.now().minusDays(days);

            // 构建查询条件
            LambdaQueryWrapper<VideoTranslateTaskPO> baseQuery = new LambdaQueryWrapper<VideoTranslateTaskPO>()
                    .eq(VideoTranslateTaskPO::getIsDeleted, 0)
                    .ge(VideoTranslateTaskPO::getCreatedTime, startTime);

            if (userId != null && !userId.trim().isEmpty()) {
                baseQuery.eq(VideoTranslateTaskPO::getUserId, userId);
            }

            // 查询数据
            List<VideoTranslateTaskPO> tasks = videoTranslateTaskMapper.selectList(
                    baseQuery.orderByAsc(VideoTranslateTaskPO::getCreatedTime));

            // 按时间粒度分组统计
            Map<String, Object> trendData = new HashMap<>();
            List<Map<String, Object>> timeSeriesData = generateTimeSeriesData(tasks, days, granularity);

            trendData.put("timeSeriesData", timeSeriesData);
            trendData.put("totalTasks", tasks.size());
            trendData.put("granularity", granularity);
            trendData.put("days", days);
            trendData.put("startTime", startTime);
            trendData.put("endTime", LocalDateTime.now());

            return Result.SUCCESS(trendData);

        } catch (Exception e) {
            log.error("[{}] 获取任务执行趋势异常: error={}", methodName, e.getMessage(), e);
            return Result.ERROR("获取任务执行趋势异常: " + e.getMessage());
        }
    }

    /**
     * 生成导出文件名
     */
    private String generateExportFileName(String userId, String format) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String userPart = userId != null ? "_" + userId : "";
        return "video_translate_history" + userPart + "_" + timestamp + "." + format.toLowerCase();
    }

    /**
     * 生成时间序列数据
     */
    private List<Map<String, Object>> generateTimeSeriesData(List<VideoTranslateTaskPO> tasks,
            Integer days, String granularity) {
        List<Map<String, Object>> timeSeriesData = new ArrayList<>();

        // 根据粒度生成时间点
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        LocalDateTime currentTime = startTime;
        LocalDateTime endTime = LocalDateTime.now();

        while (currentTime.isBefore(endTime)) {
            LocalDateTime nextTime;
            String timeLabel;

            switch (granularity.toUpperCase()) {
                case "HOUR":
                    nextTime = currentTime.plusHours(1);
                    timeLabel = currentTime.format(DateTimeFormatter.ofPattern("MM-dd HH:00"));
                    break;
                case "WEEK":
                    nextTime = currentTime.plusWeeks(1);
                    timeLabel = currentTime.format(DateTimeFormatter.ofPattern("MM-dd")) + " 周";
                    break;
                default: // DAY
                    nextTime = currentTime.plusDays(1);
                    timeLabel = currentTime.format(DateTimeFormatter.ofPattern("MM-dd"));
                    break;
            }

            // 统计该时间段内的任务
            final LocalDateTime periodStart = currentTime;
            final LocalDateTime periodEnd = nextTime;

            long totalCount = tasks.stream()
                    .filter(task -> !task.getCreatedTime().isBefore(periodStart) &&
                            task.getCreatedTime().isBefore(periodEnd))
                    .count();

            long successCount = tasks.stream()
                    .filter(task -> !task.getCreatedTime().isBefore(periodStart) &&
                            task.getCreatedTime().isBefore(periodEnd) &&
                            task.getStatus() == 2) // 2 = 翻译成功
                    .count();

            long failedCount = tasks.stream()
                    .filter(task -> !task.getCreatedTime().isBefore(periodStart) &&
                            task.getCreatedTime().isBefore(periodEnd) &&
                            (task.getStatus() == 3 || task.getStatus() == 4)) // 3 = 失败, 4 = 超时
                    .count();

            Map<String, Object> dataPoint = new HashMap<>();
            dataPoint.put("time", timeLabel);
            dataPoint.put("timestamp", periodStart);
            dataPoint.put("totalCount", totalCount);
            dataPoint.put("successCount", successCount);
            dataPoint.put("failedCount", failedCount);
            dataPoint.put("successRate", totalCount > 0 ? (successCount * 100.0 / totalCount) : 0);

            timeSeriesData.add(dataPoint);
            currentTime = nextTime;
        }

        return timeSeriesData;
    }

    /**
     * 格式化时长
     */
    private String formatDuration(long seconds) {
        if (seconds < 60) {
            return seconds + "秒";
        } else if (seconds < 3600) {
            return (seconds / 60) + "分" + (seconds % 60) + "秒";
        } else {
            long hours = seconds / 3600;
            long minutes = (seconds % 3600) / 60;
            long secs = seconds % 60;
            return hours + "小时" + minutes + "分" + secs + "秒";
        }
    }

    /**
     * 将对象转换为JSON字符串
     */
    private String convertToJson(Object obj) {
        try {
            return new ObjectMapper().writeValueAsString(obj);
        } catch (Exception e) {
            log.error("转换JSON失败: {}", e.getMessage(), e);
            return "{}";
        }
    }

    /**
     * 转换数字状态为字符串状态
     */
    private String convertIntegerStatusToString(Integer status) {
        if (status == null)
            return "unknown";
        switch (status) {
            case 0:
                return "submitted"; // 排队中
            case 1:
                return "processing"; // 进行中
            case 2:
                return "completed"; // 翻译成功
            case 3:
                return "failed"; // 失败
            case 4:
                return "timeout"; // 超时
            case 5:
                return "cancelled"; // 已取消
            default:
                return "unknown";
        }
    }

    /**
     * 从请求参数JSON中提取信息
     */
    private void extractFromRequestParams(VideoTranslateStatusVO vo, String requestParamsJson) {
        if (StringUtils.isBlank(requestParamsJson))
            return;
        try {
            ObjectMapper mapper = new ObjectMapper();
            @SuppressWarnings("unchecked")
            Map<String, Object> params = mapper.readValue(requestParamsJson, Map.class);
            vo.setSourceLanguage((String) params.get("sourceLanguage"));
            vo.setTargetLanguage((String) params.get("targetLanguage"));
            vo.setVoiceId((String) params.get("voiceId"));
        } catch (Exception e) {
            log.warn("解析请求参数JSON失败: {}", e.getMessage());
        }
    }

    /**
     * 从结果JSON中提取信息
     */
    private void extractFromResultJson(VideoTranslateStatusVO vo, String resultJson) {
        if (StringUtils.isBlank(resultJson))
            return;
        try {
            ObjectMapper mapper = new ObjectMapper();
            @SuppressWarnings("unchecked")
            Map<String, Object> result = mapper.readValue(resultJson, Map.class);
            vo.setOriginalText((String) result.get("originalText"));
            vo.setTranslatedText((String) result.get("translatedText"));
            vo.setSubtitleUrl((String) result.get("subtitleUrl"));
        } catch (Exception e) {
            log.warn("解析结果JSON失败: {}", e.getMessage());
        }
    }
}
